package com.data.receive.nn.controller;

import com.data.receive.nn.service.CardSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 门禁刷卡信息同步控制器
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Slf4j
@RestController
@RequestMapping("/api/card")
public class CardSyncController {

    @Autowired
    private CardSyncService cardSyncService;

    /**
     * 同步门禁刷卡信息
     * 
     * @return 同步结果
     */
    @GetMapping("/sync")
    public ResponseEntity<Map<String, Object>> syncCardRecords() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int syncCount = cardSyncService.syncCardRecords();
            
            result.put("success", true);
            result.put("message", "门禁刷卡信息同步成功");
            result.put("syncCount", syncCount);
            
            log.info("门禁刷卡信息同步完成，同步记录数: {}", syncCount);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("门禁刷卡信息同步失败", e);
            
            result.put("success", false);
            result.put("message", "门禁刷卡信息同步失败: " + e.getMessage());
            result.put("syncCount", 0);
            
            return ResponseEntity.status(500).body(result);
        }
    }
}
