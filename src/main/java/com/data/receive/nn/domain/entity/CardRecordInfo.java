package com.data.receive.nn.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门禁刷卡记录信息实体类
 * 对应表：sodb_biz_card_record_info
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
@TableName("sodb_biz_card_record_info")
public class CardRecordInfo {

    /**
     * 交易历史ID
     */
    private Long trnHisId;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 证件号码
     */
    private String paperNumber;

    /**
     * 人员照片URL
     */
    private String personPic;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
