package com.data.receive.nn.controller;

import com.data.receive.nn.dao.CardRecordMapper;
import com.data.receive.nn.dao.SqlServerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据源测试控制器
 * 
 * <AUTHOR>
 * @date 2025/09/02
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class DataSourceTestController {

    @Autowired
    private CardRecordMapper cardRecordMapper;

    @Autowired
    private SqlServerMapper sqlServerMapper;

    /**
     * 测试MySQL连接
     */
    @GetMapping("/mysql")
    public ResponseEntity<Map<String, Object>> testMysql() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Long maxTrnHisId = cardRecordMapper.getMaxTrnHisId();
            result.put("success", true);
            result.put("message", "MySQL连接成功");
            result.put("maxTrnHisId", maxTrnHisId);
            log.info("MySQL连接测试成功，最大TrnHisId: {}", maxTrnHisId);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("MySQL连接测试失败", e);
            result.put("success", false);
            result.put("message", "MySQL连接失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 测试SQL Server连接
     */
    @GetMapping("/sqlserver")
    public ResponseEntity<Map<String, Object>> testSqlServer() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 尝试查询SQL Server数据
            var trnHisList = sqlServerMapper.getTrnHisWithCrdHis(0L);
            result.put("success", true);
            result.put("message", "SQL Server连接成功");
            result.put("recordCount", trnHisList.size());
            log.info("SQL Server连接测试成功，查询到记录数: {}", trnHisList.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("SQL Server连接测试失败", e);
            result.put("success", false);
            result.put("message", "SQL Server连接失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }
}
